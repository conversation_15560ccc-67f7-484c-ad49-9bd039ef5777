<template>
	<view class="main">
		<image class="avatar"
			src="https://aiszr-jiajs.oss-cn-beijing.aliyuncs.com/digital-human/videos/1752650173403149.mp4?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto"
			mode=""></image>
		<view>
			<view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">姓名</view>
			</view>
			<input type="text" v-model="name" maxlength="20" class="r-input" placeholder="请输入"
				placeholder-class="placeholder" />
		</view>
		<view>
			<view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">简介</view>
			</view>
			<textarea type="text" v-model="desc" maxlength="200" class="r-input" placeholder="请输入"
				placeholder-class="placeholder" />
		</view>
		<view v-for="(item,index) in top_video_content" :key="index">
			<view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">{{`第${index + 1}条置顶文案`}}</view>
			</view>
			<textarea type="text" v-model="top_video_content[index]" maxlength="200" class="r-input" placeholder="请输入"
				placeholder-class="placeholder" />
		</view>
		<view style="height: 140rpx;"></view>
		<view class="fixed">
			<view class="but2" @click="reset()">重新生成</view>
			<view class="but2" @click="save()">保存</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				desc: '',
				name: '',
				top_video_content: []
			}
		},
		methods: {
			async reset() {
				const result = await this.$http.post({
					url: this.$api.getAccount,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.desc = result.data.des
					this.name = result.data.name
					this.top_video_content = result.data.top_video_content
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},
			async save() {
				const result = await this.$http.post({
					url: this.$api.getAccount,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.desc = result.data.des
					this.name = result.data.name
					this.top_video_content = result.data.top_video_content
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},
			async getData() {
				const result = await this.$http.post({
					url: this.$api.getAccount,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.desc = result.data.des
					this.name = result.data.name
					this.top_video_content = result.data.top_video_content
				} else {
					this.$sun.toast(result.message, 'none');
				}
			}
		},
		onLoad() {
			this.getData()
		}
	}
</script>

<style lang="scss">
	.fixed {
		position: fixed;
		bottom: 50rpx;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-around;
		z-index: 999;

		.but2 {
			width: 300rpx;
			text-align: center;
			border-radius: 16rpx;
			box-shadow: 2px 3px 14px 0px rgba(30, 156, 214, 0.81), inset 0px 0px 11px 0px rgba(204, 235, 255, 0.3);
			background: linear-gradient(90.00deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);
			color: #FFF;
			font-size: 32rpx;
			font-weight: 600;
			padding: 22rpx 0;
		}

	}

	.r-line {
		width: 12rpx;
		height: 32rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(109, 221, 245), rgb(66, 72, 244) 100%);
		margin-right: 14rpx;
	}

	.r-input {
		width: 670rpx;
		padding: 20rpx;
		background-color: #192236;
		border-radius: 10rpx;
		color: #FFF;
	}

	.main {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx;
		gap: 20rpx;
	}

	.avatar {
		width: 180rpx;
		height: 180rpx;
		border-radius: 50%;
	}

	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}
</style>